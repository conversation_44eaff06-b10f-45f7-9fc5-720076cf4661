{"name": "update-projects", "version": "1.0.0", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder"}, "dependencies": {"body-parser": "^1.20.2"}, "devDependencies": {"electron": "^31.0.0", "electron-builder": "^24.13.3"}, "build": {"appId": "com.example.updateprojects", "productName": "Update Projects", "files": ["main.js", "renderer.js", "preload.js", "views/**/*", "pcProject.json", "mobileProject.json", "version.json"], "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}}}