{"name": "update-projects", "version": "1.0.0", "main": "main.js", "scripts": {"start": "electron .", "generate-icons": "node scripts/generate-icons.js", "postinstall": "electron-builder install-app-deps", "build": "electron-builder", "build:win": "electron-builder --win", "build:win32": "electron-builder --win --ia32", "build:win64": "electron-builder --win --x64"}, "dependencies": {"body-parser": "^1.20.2", "node-fetch": "^2.6.7", "extract-zip": "^2.0.1", "tar": "^6.1.15"}, "devDependencies": {"electron": "^31.0.0", "electron-builder": "^24.13.3", "png2icons": "^2.0.1", "sharp": "^0.32.1"}, "build": {"appId": "com.example.updateprojects", "productName": "Update Projects", "files": ["main.js", "renderer.js", "preload.js", "views/**/*", "pcProject.json", "mobileProject.json", "version.json"], "directories": {"output": "dist", "buildResources": "build"}, "mac": {"target": "dmg", "icon": "build/icons/icon.icns"}, "win": {"target": "nsis", "icon": "build/icons/icon.ico"}, "linux": {"target": "AppImage", "icon": "build/icons/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico"}, "dmg": {"icon": "build/icons/icon.icns", "title": "${productName} ${version}"}}}