// renderer.js
document.addEventListener('DOMContentLoaded', async () => {
  const projectList = document.getElementById('project-list');
  const versionInput = document.getElementById('version');
  const nextButton = document.getElementById('next-button');
  const errorMessage = document.getElementById('error-message');

  try {
    // 使用通过 contextBridge 暴露的 API
    const { projects, pcProjectsCount, mobileProjectsCount, lastPcVersion, lastMobileVersion } = await window.electronAPI.loadProjects();

    // 填充项目列表
    projects.forEach((proj, index) => {
      const div = document.createElement('div');
      div.className = 'form-check';
      div.innerHTML = `
        <input class="form-check-input" type="checkbox" name="projects" value="${index}" id="proj${index}">
        <label class="form-check-label" for="proj${index}">
          ${proj.name} (${proj.type === 'pc' ? '@xypaas/appEngine_pc' : '@xypaas/appEngine_mobile'}) - ${proj.path}
        </label>
      `;
      projectList.appendChild(div);
    });

    // 添加全部选项
    const allPcDiv = document.createElement('div');
    allPcDiv.className = 'form-check';
    allPcDiv.innerHTML = `
      <input class="form-check-input" type="checkbox" name="projects" value="all_pc" id="all_pc">
      <label class="form-check-label" for="all_pc">全部 PC 项目</label>
    `;
    projectList.appendChild(allPcDiv);

    const allMobileDiv = document.createElement('div');
    allMobileDiv.className = 'form-check';
    allMobileDiv.innerHTML = `
      <input class="form-check-input" type="checkbox" name="projects" value="all_mobile" id="all_mobile">
      <label class="form-check-label" for="all_mobile">全部 Mobile 项目</label>
    `;
    projectList.appendChild(allMobileDiv);

    const allDiv = document.createElement('div');
    allDiv.className = 'form-check';
    allDiv.innerHTML = `
      <input class="form-check-input" type="checkbox" name="projects" value="all" id="all">
      <label class="form-check-label" for="all">全部 PC 和 Mobile 项目</label>
    `;
    projectList.appendChild(allDiv);

    // 设置版本号默认值
    versionInput.value = lastPcVersion || lastMobileVersion || '';
    document.getElementById('version-label').innerText = `依赖版本号（上一次 PC 版本：${lastPcVersion || '无'}，Mobile 版本：${lastMobileVersion || '无'}）`;

    // 下一步按钮
    nextButton.addEventListener('click', async () => {
      const selectedProjects = Array.from(document.querySelectorAll('input[name="projects"]:checked')).map(input => input.value);
      const version = versionInput.value.trim();

      if (selectedProjects.length === 0) {
        errorMessage.innerText = '请选择至少一个项目';
        return;
      }
      if (!version) {
        errorMessage.innerText = '请输入版本号';
        return;
      }

      // 跳转到确认页面
      localStorage.setItem('selectedProjects', JSON.stringify(selectedProjects));
      localStorage.setItem('version', version);
      window.location.href = 'confirm.html';
    });
  } catch (error) {
    errorMessage.innerText = `加载项目失败：${error.message}`;
  }
});
