// renderer.js
let allProjects = [];
let currentEditingProject = null;

document.addEventListener('DOMContentLoaded', async () => {
  const projectList = document.getElementById('project-list');
  const versionInput = document.getElementById('version');
  const nextButton = document.getElementById('next-button');
  const errorMessage = document.getElementById('error-message');
  const successMessage = document.getElementById('success-message');

  // 项目管理相关元素
  const addProjectBtn = document.getElementById('add-project-btn');
  const refreshProjectsBtn = document.getElementById('refresh-projects-btn');
  const addProjectForm = document.getElementById('add-project-form');
  const saveNewProjectBtn = document.getElementById('save-new-project-btn');
  const cancelAddProjectBtn = document.getElementById('cancel-add-project-btn');
  const editProjectModal = new bootstrap.Modal(document.getElementById('editProjectModal'));
  const saveEditProjectBtn = document.getElementById('save-edit-project-btn');

  // 显示消息的辅助函数
  function showMessage(message, isError = false) {
    errorMessage.innerText = isError ? message : '';
    successMessage.innerText = isError ? '' : message;
    setTimeout(() => {
      errorMessage.innerText = '';
      successMessage.innerText = '';
    }, 3000);
  }

  // 加载项目列表
  async function loadProjects() {
    try {
      const { projects, lastPcVersion, lastMobileVersion } = await window.electronAPI.loadProjects();
      allProjects = projects;
      renderProjectList();

      // 设置版本号默认值
      versionInput.value = lastPcVersion || lastMobileVersion || '';
      document.getElementById('version-label').innerText = `依赖版本号（上一次 PC 版本：${lastPcVersion || '无'}，Mobile 版本：${lastMobileVersion || '无'}）`;
    } catch (error) {
      showMessage(`加载项目失败：${error.message}`, true);
    }
  }

  // 渲染项目列表
  function renderProjectList() {
    projectList.innerHTML = '';

    // 渲染每个项目
    allProjects.forEach((proj, index) => {
      const projectDiv = document.createElement('div');
      projectDiv.className = 'project-item d-flex align-items-center';
      projectDiv.innerHTML = `
        <div class="form-check">
          <input class="form-check-input" type="checkbox" name="projects" value="${index}" id="proj${index}">
          <label class="form-check-label" for="proj${index}">
            <div class="project-info">
              <div class="d-flex align-items-center">
                <strong>${proj.name}</strong>
                <span class="badge bg-${proj.type === 'pc' ? 'primary' : 'success'} project-type-badge ms-2">
                  ${proj.type === 'pc' ? 'PC' : 'Mobile'}
                </span>
                <span class="project-path">${proj.path}</span>
              </div>
            </div>
          </label>
        </div>
        <div class="project-actions">
          <button class="btn btn-outline-primary btn-sm me-2" onclick="editProject(${index})">
            <i class="bi bi-pencil"></i> 编辑
          </button>
          <button class="btn btn-outline-danger btn-sm" onclick="deleteProject(${index})">
            <i class="bi bi-trash"></i> 删除
          </button>
        </div>
      `;
      projectList.appendChild(projectDiv);
    });

    // 添加全部选项
    const allOptionsDiv = document.createElement('div');
    allOptionsDiv.className = 'mt-3 pt-3 border-top';
    allOptionsDiv.innerHTML = `
      <h6>批量选择</h6>
      <div style="display: flex; justify-content-between; align-items-center;">
        <div class="form-check">
          <input class="form-check-input" type="checkbox" name="projects" value="all_pc" id="all_pc">
          <label class="form-check-label" for="all_pc">全部 PC 项目</label>
        </div>
        <div class="form-check">
          <input class="form-check-input" type="checkbox" name="projects" value="all_mobile" id="all_mobile">
          <label class="form-check-label" for="all_mobile">全部 Mobile 项目</label>
        </div>
        <div class="form-check">
          <input class="form-check-input" type="checkbox" name="projects" value="all" id="all">
          <label class="form-check-label" for="all">全部 PC 和 Mobile 项目</label>
        </div>
      </div>
    `;
    projectList.appendChild(allOptionsDiv);
  }

  // 编辑项目
  window.editProject = function(index) {
    const project = allProjects[index];
    currentEditingProject = { ...project, index };

    document.getElementById('edit-project-name').value = project.name;
    document.getElementById('edit-project-path').value = project.path;
    document.getElementById('edit-project-type').value = project.type;

    editProjectModal.show();
  };

  // 删除项目
  window.deleteProject = async function(index) {
    const project = allProjects[index];

    if (confirm(`确定要删除项目 "${project.name}" 吗？`)) {
      try {
        const result = await window.electronAPI.deleteProject({
          name: project.name,
          path: project.path,
          type: project.type
        });

        if (result.success) {
          showMessage(result.message);
          await loadProjects();
        } else {
          showMessage(result.error, true);
        }
      } catch (error) {
        showMessage(`删除项目失败：${error.message}`, true);
      }
    }
  };

  // 添加项目按钮事件
  addProjectBtn.addEventListener('click', () => {
    addProjectForm.style.display = 'block';
    document.getElementById('new-project-name').value = '';
    document.getElementById('new-project-path').value = '';
    document.getElementById('new-project-type').value = 'pc';
  });

  // 取消添加项目
  cancelAddProjectBtn.addEventListener('click', () => {
    addProjectForm.style.display = 'none';
  });

  // 保存新项目
  saveNewProjectBtn.addEventListener('click', async () => {
    const name = document.getElementById('new-project-name').value.trim();
    const path = document.getElementById('new-project-path').value.trim();
    const type = document.getElementById('new-project-type').value;

    if (!name || !path) {
      showMessage('请填写项目名称和路径', true);
      return;
    }

    try {
      const result = await window.electronAPI.saveProject({ name, path, type });

      if (result.success) {
        showMessage(result.message);
        addProjectForm.style.display = 'none';
        await loadProjects();
      } else {
        showMessage(result.error, true);
      }
    } catch (error) {
      showMessage(`添加项目失败：${error.message}`, true);
    }
  });

  // 保存编辑的项目
  saveEditProjectBtn.addEventListener('click', async () => {
    if (!currentEditingProject) return;

    const name = document.getElementById('edit-project-name').value.trim();
    const path = document.getElementById('edit-project-path').value.trim();
    const type = document.getElementById('edit-project-type').value;

    if (!name || !path) {
      showMessage('请填写项目名称和路径', true);
      return;
    }

    try {
      const result = await window.electronAPI.updateProject({
        originalName: currentEditingProject.name,
        originalPath: currentEditingProject.path,
        originalType: currentEditingProject.type,
        name,
        path,
        type
      });

      if (result.success) {
        showMessage(result.message);
        editProjectModal.hide();
        currentEditingProject = null;
        await loadProjects();
      } else {
        showMessage(result.error, true);
      }
    } catch (error) {
      showMessage(`更新项目失败：${error.message}`, true);
    }
  });

  // 刷新项目列表
  refreshProjectsBtn.addEventListener('click', async () => {
    await loadProjects();
    showMessage('项目列表已刷新');
  });

  // 下一步按钮
  nextButton.addEventListener('click', async () => {
    const selectedProjects = Array.from(document.querySelectorAll('input[name="projects"]:checked')).map(input => input.value);
    const version = versionInput.value.trim();

    if (selectedProjects.length === 0) {
      showMessage('请选择至少一个项目', true);
      return;
    }
    if (!version) {
      showMessage('请输入版本号', true);
      return;
    }

    // 跳转到确认页面
    localStorage.setItem('selectedProjects', JSON.stringify(selectedProjects));
    localStorage.setItem('version', version);
    window.location.href = 'confirm.html';
  });

  // 初始加载项目
  await loadProjects();
});
