<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目更新</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    .form-check {
      margin: 8px 0;
      flex-grow: 1;
    }
    .form-check-label {
      cursor: pointer;
    }
    .project-item {
      border: 1px solid #dee2e6;
      border-radius: 0.375rem;
      padding: 4px;
      margin-bottom: 6px;
      background-color: #f8f9fa;
      justify-content: space-between;
      display: flex;
      align-items: center;
    }
    .project-actions {
    }
    .project-info {
      flex-grow: 1;
    }
    .project-path {
      font-size: 0.9em;
      color: #6c757d;
      margin-left: 5px;
    }
    .project-type-badge {
      font-size: 0.8em;
    }
    .add-project-form {
      background-color: #e9ecef;
      border-radius: 0.375rem;
      padding: 20px;
      margin-bottom: 20px;
      display: none;
    }
  </style>
</head>
<body class="container mt-5">
  <h2>@xypaas/appEngine更新</h2>
  <div id="error-message" class="text-danger mb-3"></div>
  <div id="success-message" class="text-success mb-3"></div>

  <!-- 项目管理区域 -->
  <div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h3>项目列表</h3>
      <div>
        <button id="add-project-btn" class="btn btn-success btn-sm">
          <i class="bi bi-plus-circle"></i> 添加项目
        </button>
        <button id="refresh-projects-btn" class="btn btn-outline-secondary btn-sm">
          <i class="bi bi-arrow-clockwise"></i> 刷新
        </button>
      </div>
    </div>

    <!-- 添加项目表单 -->
    <div id="add-project-form" class="add-project-form">
      <h5>添加新项目</h5>
      <div class="row">
        <div class="col-md-4">
          <label for="new-project-name" class="form-label">项目名称</label>
          <input type="text" class="form-control" id="new-project-name" placeholder="输入项目名称">
        </div>
        <div class="col-md-6">
          <label for="new-project-path" class="form-label">项目路径</label>
          <input type="text" class="form-control" id="new-project-path" placeholder="输入项目路径">
        </div>
        <div class="col-md-2">
          <label for="new-project-type" class="form-label">项目类型</label>
          <select class="form-select" id="new-project-type">
            <option value="pc">PC</option>
            <option value="mobile">Mobile</option>
          </select>
        </div>
      </div>
      <div class="mt-3">
        <button id="save-new-project-btn" class="btn btn-primary btn-sm">
          <i class="bi bi-check-circle"></i> 保存
        </button>
        <button id="cancel-add-project-btn" class="btn btn-secondary btn-sm">
          <i class="bi bi-x-circle"></i> 取消
        </button>
      </div>
    </div>

    <!-- 项目列表 -->
    <div id="project-list"></div>
  </div>

  <!-- 版本输入区域 -->
  <div class="mb-3">
    <label for="version" class="form-label" id="version-label"></label>
    <input type="text" class="form-control" id="version" name="version">
  </div>

  <div class="d-flex justify-content-end align-items-center mb-3">
    <button id="next-button" class="btn btn-primary">下一步</button>
  </div>

  <!-- 编辑项目模态框 -->
  <div class="modal fade xl" id="editProjectModal" tabindex="-1">
    <div class="modal-dialog" style="max-width: 80vw">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">编辑项目</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label for="edit-project-name" class="form-label">项目名称</label>
            <input type="text" class="form-control" id="edit-project-name">
          </div>
          <div class="mb-3">
            <label for="edit-project-path" class="form-label">项目路径</label>
            <input type="text" class="form-control" id="edit-project-path">
          </div>
          <div class="mb-3">
            <label for="edit-project-type" class="form-label">项目类型</label>
            <select class="form-select" id="edit-project-type">
              <option value="pc">PC</option>
              <option value="mobile">Mobile</option>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-edit-project-btn">保存更改</button>
        </div>
      </div>
    </div>
  </div>

  <script src="../renderer.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>