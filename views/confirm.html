<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>确认更新</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    #log-container {
      height: 300px;
      overflow-y: auto;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 0.25rem;
      padding: 10px;
      margin-top: 20px;
      font-family: monospace;
    }
    .log-entry {
      margin-bottom: 5px;
      padding: 5px;
      border-radius: 3px;
    }
    .log-info {
      background-color: #e9ecef;
    }
    .log-success {
      background-color: #d4edda;
      color: #155724;
    }
    .log-error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .log-warning {
      background-color: #fff3cd;
      color: #856404;
    }
    .log-time {
      color: #6c757d;
      font-size: 0.8rem;
      margin-right: 10px;
    }
  </style>
</head>
<body class="container my-5">
  <h1>确认更新</h1>
  <div id="error-message" class="text-danger mb-3"></div>
  <h3>选中的项目</h3>
  <ul class="list-group mb-3" id="project-list"></ul>
  <h3>版本号</h3>
  <p id="version"></p>
  <div id="version-warning" class="alert alert-warning" style="display: none;"></div>
  
  <div class="mb-3" style="display: flex; justify-content: flex-end; align-items: center;">
    <button id="execute-button" class="btn btn-success">确认执行</button>
    <a href="index.html" class="btn btn-secondary" style="margin-left: 16px;">返回修改</a>
  </div>
  
  <h3>执行日志</h3>
  <div id="log-container"></div>
  
  <script>
    document.addEventListener('DOMContentLoaded', async () => {
      const selectedProjects = JSON.parse(localStorage.getItem('selectedProjects') || '[]');
      const version = localStorage.getItem('version') || '';
      const projectList = document.getElementById('project-list');
      const versionElement = document.getElementById('version');
      const versionWarning = document.getElementById('version-warning');
      const errorMessage = document.getElementById('error-message');
      const logContainer = document.getElementById('log-container');
      
      // 添加日志显示功能
      window.electronAPI.onCommandLog((logData) => {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${logData.type}`;
        
        const timeSpan = document.createElement('span');
        timeSpan.className = 'log-time';
        timeSpan.textContent = logData.time;
        
        const messageSpan = document.createElement('span');
        messageSpan.textContent = logData.message;
        
        logEntry.appendChild(timeSpan);
        logEntry.appendChild(messageSpan);
        logContainer.appendChild(logEntry);
        
        // 自动滚动到底部
        logContainer.scrollTop = logContainer.scrollHeight;
      });
      
      try {
        const { projects } = await window.electronAPI.loadProjects();
        const selectedProjectsData = selectedProjects.map(index => {
          if (index === 'all' || index === 'all_pc' || index === 'all_mobile') return null;
          const idx = parseInt(index, 10);
          return !isNaN(idx) && idx >= 0 && idx < projects.length ? projects[idx] : null;
        }).filter(Boolean);

        if (selectedProjects.includes('all')) {
          selectedProjectsData.push(...projects);
        } else if (selectedProjects.includes('all_pc')) {
          selectedProjectsData.push(...projects.filter(p => p.type === 'pc'));
        } else if (selectedProjects.includes('all_mobile')) {
          selectedProjectsData.push(...projects.filter(p => p.type === 'mobile'));
        }

        const uniqueProjects = [...new Map(selectedProjectsData.map(proj => [proj.path, proj])).values()];
        uniqueProjects.forEach(proj => {
          const li = document.createElement('li');
          li.className = 'list-group-item';
          li.innerText = `${proj.name} (${proj.type === 'pc' ? '@xypaas/appEngine_pc' : '@xypaas/appEngine_mobile'}) - ${proj.path}`;
          projectList.appendChild(li);
        });

        versionElement.innerText = version;

        const versionData = await window.electronAPI.loadProjects();
        const selectedTypes = [...new Set(uniqueProjects.map(proj => proj.type))];
        let lastVersion = null;
        if (selectedTypes.includes('pc') && versionData.lastPcVersion) {
          lastVersion = versionData.lastPcVersion;
        } else if (selectedTypes.includes('mobile') && versionData.lastMobileVersion) {
          lastVersion = versionData.lastMobileVersion;
        }
        if (lastVersion && lastVersion === version) {
          versionWarning.style.display = 'block';
          versionWarning.innerText = `警告：输入的版本号 ${version} 与上次相同，确认继续执行？`;
        }

        document.getElementById('execute-button').addEventListener('click', async () => {
          // 清空日志容器
          logContainer.innerHTML = '';
          document.getElementById('execute-button').disabled = true;
          
          try {
            const { executedProjects, skippedProjects } = await window.electronAPI.executeUpdate({ projects: selectedProjects, version });
            localStorage.setItem('executedProjects', JSON.stringify(executedProjects));
            localStorage.setItem('skippedProjects', JSON.stringify(skippedProjects));
            
            // 添加一个延迟，让用户有时间查看日志
            setTimeout(() => {
              // window.location.href = 'result.html';
              document.getElementById('execute-button').disabled = false;
            }, 1000);
          } catch (error) {
            errorMessage.innerText = `执行失败：${error.message}`;
          }
        });
      } catch (error) {
        errorMessage.innerText = `加载确认页面失败：${error.message}`;
      }
    });
  </script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
