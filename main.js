// main.js
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const { execSync } = require('child_process');
const fetch = require('node-fetch');
const extractZip = require('extract-zip');
const tar = require('tar');

// 配置文件路径
const PC_CONFIG_PATH = path.join(__dirname, 'pcProject.json');
const MOBILE_CONFIG_PATH = path.join(__dirname, 'mobileProject.json');
const VERSION_FILE_PATH = path.join(__dirname, 'version.json');

// 工具版本配置
const TOOL_VERSIONS = {
  NODE_VERSION: '18.16.0',
  PNPM_VERSION: '8.15.9'
};

// 工具下载路径
const TOOLS_DIR = path.join(__dirname, 'tools');
const NODE_DIR = path.join(TOOLS_DIR, `node-v${TOOL_VERSIONS.NODE_VERSION}`);
const PNPM_DIR = path.join(TOOLS_DIR, 'pnpm');

// 确保工具目录存在
async function ensureToolsDirectory() {
  try {
    await fs.mkdir(TOOLS_DIR, { recursive: true });
  } catch (error) {
    console.error('创建工具目录失败:', error);
  }
}

// 检查工具是否已安装
async function checkToolInstalled(toolPath) {
  try {
    await fs.access(toolPath);
    return true;
  } catch {
    return false;
  }
}

// 下载文件
async function downloadFile(url, outputPath, mainWindow) {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`下载失败: ${response.statusText}`);
  }

  const fileStream = require('fs').createWriteStream(outputPath);
  const totalSize = parseInt(response.headers.get('content-length'), 10);
  let downloadedSize = 0;

  return new Promise((resolve, reject) => {
    response.body.on('data', (chunk) => {
      downloadedSize += chunk.length;
      const progress = Math.round((downloadedSize / totalSize) * 100);

      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('command-log', {
          type: 'info',
          message: `下载进度: ${progress}%`,
          time: new Date().toLocaleTimeString()
        });
      }
    });

    response.body.pipe(fileStream);
    response.body.on('error', reject);
    fileStream.on('finish', resolve);
    fileStream.on('error', reject);
  });
}

// 下载并安装 Node.js
async function downloadAndInstallNode(mainWindow) {
  const platform = process.platform;
  const arch = process.arch;

  let nodeUrl, nodeFileName, nodeExtractPath;

  if (platform === 'win32') {
    nodeFileName = `node-v${TOOL_VERSIONS.NODE_VERSION}-win-${arch === 'x64' ? 'x64' : 'x86'}.zip`;
    nodeUrl = `https://nodejs.org/dist/v${TOOL_VERSIONS.NODE_VERSION}/${nodeFileName}`;
    nodeExtractPath = path.join(TOOLS_DIR, nodeFileName);
  } else if (platform === 'darwin') {
    nodeFileName = `node-v${TOOL_VERSIONS.NODE_VERSION}-darwin-${arch}.tar.gz`;
    nodeUrl = `https://nodejs.org/dist/v${TOOL_VERSIONS.NODE_VERSION}/${nodeFileName}`;
    nodeExtractPath = path.join(TOOLS_DIR, nodeFileName);
  } else {
    nodeFileName = `node-v${TOOL_VERSIONS.NODE_VERSION}-linux-${arch}.tar.xz`;
    nodeUrl = `https://nodejs.org/dist/v${TOOL_VERSIONS.NODE_VERSION}/${nodeFileName}`;
    nodeExtractPath = path.join(TOOLS_DIR, nodeFileName);
  }

  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('command-log', {
      type: 'info',
      message: `开始下载 Node.js ${TOOL_VERSIONS.NODE_VERSION}...`,
      time: new Date().toLocaleTimeString()
    });
  }

  // 下载 Node.js
  await downloadFile(nodeUrl, nodeExtractPath, mainWindow);

  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('command-log', {
      type: 'info',
      message: `开始解压 Node.js...`,
      time: new Date().toLocaleTimeString()
    });
  }

  // 解压文件
  if (platform === 'win32') {
    await extractZip(nodeExtractPath, { dir: TOOLS_DIR });
  } else {
    await tar.x({
      file: nodeExtractPath,
      cwd: TOOLS_DIR
    });
  }

  // 删除下载的压缩文件
  await fs.unlink(nodeExtractPath);

  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('command-log', {
      type: 'success',
      message: `Node.js ${TOOL_VERSIONS.NODE_VERSION} 安装完成`,
      time: new Date().toLocaleTimeString()
    });
  }
}

// 下载并安装 pnpm
async function downloadAndInstallPnpm(mainWindow) {
  const pnpmUrl = `https://github.com/pnpm/pnpm/releases/download/v${TOOL_VERSIONS.PNPM_VERSION}/pnpm-${process.platform}-${process.arch}`;
  const pnpmPath = path.join(PNPM_DIR, process.platform === 'win32' ? 'pnpm.exe' : 'pnpm');

  await fs.mkdir(PNPM_DIR, { recursive: true });

  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('command-log', {
      type: 'info',
      message: `开始下载 pnpm ${TOOL_VERSIONS.PNPM_VERSION}...`,
      time: new Date().toLocaleTimeString()
    });
  }

  await downloadFile(pnpmUrl, pnpmPath, mainWindow);

  // 设置执行权限 (非 Windows 系统)
  if (process.platform !== 'win32') {
    await fs.chmod(pnpmPath, '755');
  }

  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('command-log', {
      type: 'success',
      message: `pnpm ${TOOL_VERSIONS.PNPM_VERSION} 安装完成`,
      time: new Date().toLocaleTimeString()
    });
  }
}

// 获取工具路径
function getToolPaths() {
  const platform = process.platform;
  const arch = process.arch;

  let nodePath, pnpmPath;

  if (platform === 'win32') {
    nodePath = path.join(NODE_DIR, 'node.exe');
    pnpmPath = path.join(PNPM_DIR, 'pnpm.exe');
  } else {
    nodePath = path.join(NODE_DIR, 'bin', 'node');
    pnpmPath = path.join(PNPM_DIR, 'pnpm');
  }

  return { nodePath, pnpmPath };
}

// 确保工具已安装
async function ensureToolsInstalled(mainWindow) {
  await ensureToolsDirectory();

  const { nodePath, pnpmPath } = getToolPaths();

  // 检查并安装 Node.js
  if (!(await checkToolInstalled(nodePath))) {
    await downloadAndInstallNode(mainWindow);
  }

  // 检查并安装 pnpm
  if (!(await checkToolInstalled(pnpmPath))) {
    await downloadAndInstallPnpm(mainWindow);
  }
}

// 检查 Git 是否有未提交更改
const hasUncommittedChanges = (projectPath, mainWindow) => {
  try {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `检查 Git 状态: ${projectPath}`,
        time: new Date().toLocaleTimeString()
      });
    }
    
    const status = execSync('git status --porcelain', { cwd: projectPath, stdio: 'pipe' }).toString();
    const hasChanges = status.trim().length > 0;
    
    if (hasChanges && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'warning',
        message: `检测到未提交更改: ${projectPath}`,
        time: new Date().toLocaleTimeString()
      });
    }
    
    return hasChanges;
  } catch (error) {
    const errorMessage = `在 ${projectPath} 检查 Git 状态时出错：${error.message}`;
    console.error(errorMessage);
    
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'error',
        message: errorMessage,
        time: new Date().toLocaleTimeString()
      });
    }
    
    return true; // 出错时视为有未提交更改，跳过
  }
};

// 执行 shell 命令
const executeCommand = async (command, projectPath, mainWindow) => {
  try {
    // 确保工具已安装
    await ensureToolsInstalled(mainWindow);

    const { nodePath, pnpmPath } = getToolPaths();

    // 替换命令中的 node 和 pnpm 为指定版本的路径
    let actualCommand = command;
    if (command.includes('pnpm')) {
      actualCommand = command.replace(/^pnpm/, `"${pnpmPath}"`);
      // 设置 Node.js 环境变量
      process.env.PNPM_NODE_PATH = nodePath;
    }

    const logMessage = `执行命令：${actualCommand} 在 ${projectPath}`;
    console.log(logMessage);

    // 发送日志到渲染进程
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: logMessage,
        time: new Date().toLocaleTimeString()
      });
    }

    // 设置环境变量
    const env = {
      ...process.env,
      NODE_PATH: nodePath,
      PATH: `${path.dirname(nodePath)}:${path.dirname(pnpmPath)}:${process.env.PATH}`
    };

    execSync(actualCommand, {
      cwd: projectPath,
      stdio: 'pipe',
      env: env
    });

    const successMessage = `命令执行成功：${actualCommand}`;
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'success',
        message: successMessage,
        time: new Date().toLocaleTimeString()
      });
    }

    return { success: true };
  } catch (error) {
    const errorMessage = `在 ${projectPath} 执行 ${command} 时出错：${error.message}`;
    console.error(errorMessage);

    // 发送错误日志到渲染进程
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'error',
        message: errorMessage,
        time: new Date().toLocaleTimeString()
      });
    }

    return { success: false, error: error.message };
  }
};

// 读取 version.json 文件
async function readVersionFile() {
  try {
    const content = await fs.readFile(VERSION_FILE_PATH, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    return { pc: null, mobile: null }; // 文件不存在或无效时返回默认值
  }
}

// 保存版本到 version.json
async function saveVersionFile(type, version) {
  try {
    const versionData = await readVersionFile();
    versionData[type] = version;
    await fs.writeFile(VERSION_FILE_PATH, JSON.stringify(versionData, null, 2));
  } catch (error) {
    console.error(`保存版本到 ${VERSION_FILE_PATH} 失败：${error.message}`);
  }
}

// 创建主窗口
function createWindow() {
  console.log('应用路径:', app.getAppPath());
  console.log('预加载脚本路径:', path.join(__dirname, 'preload.js'));

  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 1200,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 添加错误处理
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('页面加载失败:', errorCode, errorDescription);
  });

  // 添加控制台日志捕获
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`[Renderer Console][${level}]: ${message}`);
  });

  mainWindow.loadFile(path.join(__dirname, 'views/index.html'));
  
  // // 开发模式下打开开发者工具
  // if (process.env.NODE_ENV === 'development') {
  //   mainWindow.webContents.openDevTools();
  // } else {
  //   // 生产环境下延迟加载，给预加载脚本更多时间
  //   // setTimeout(() => {
  //   //   mainWindow.webContents.send('app-ready', true);
  //   // }, 2000);
  // }
}

// Electron 应用初始化
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC 通信：加载项目数据
ipcMain.handle('load-projects', async () => {
  const allProjects = [];
  let pcProjects = [];
  let mobileProjects = [];
  try {
    const pcConfig = JSON.parse(await fs.readFile(PC_CONFIG_PATH, 'utf-8'));
    pcProjects = pcConfig.projects.map((proj) => ({ ...proj, type: 'pc', dependency: '@xypaas/appEngine_pc' }));
    allProjects.push(...pcProjects);
  } catch (error) {
    throw new Error(`读取 ${PC_CONFIG_PATH} 失败：${error.message}`);
  }
  try {
    const mobileConfig = JSON.parse(await fs.readFile(MOBILE_CONFIG_PATH, 'utf-8'));
    mobileProjects = mobileConfig.projects.map((proj) => ({ ...proj, type: 'mobile', dependency: '@xypaas/appEngine_mobile' }));
    allProjects.push(...mobileProjects);
  } catch (error) {
    throw new Error(`读取 ${MOBILE_CONFIG_PATH} 失败：${error.message}`);
  }

  const versionData = await readVersionFile();
  return {
    projects: allProjects,
    pcProjectsCount: pcProjects.length,
    mobileProjectsCount: mobileProjects.length,
    lastPcVersion: versionData.pc || '',
    lastMobileVersion: versionData.mobile || ''
  };
});

// 保存项目配置到文件
async function saveProjectsToFile(type, projects) {
  const configPath = type === 'pc' ? PC_CONFIG_PATH : MOBILE_CONFIG_PATH;
  const config = { projects };
  await fs.writeFile(configPath, JSON.stringify(config, null, 2));
}

// 读取项目配置文件
async function readProjectsFromFile(type) {
  const configPath = type === 'pc' ? PC_CONFIG_PATH : MOBILE_CONFIG_PATH;
  try {
    const content = await fs.readFile(configPath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    return { projects: [] };
  }
}

// IPC 通信：保存新项目
ipcMain.handle('save-project', async (event, { name, path, type }) => {
  try {
    const config = await readProjectsFromFile(type);

    // 检查项目是否已存在
    const existingProject = config.projects.find(p => p.name === name || p.path === path);
    if (existingProject) {
      throw new Error('项目名称或路径已存在');
    }

    // 添加新项目
    config.projects.push({ name, path });
    await saveProjectsToFile(type, config.projects);

    return { success: true, message: '项目添加成功' };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// IPC 通信：更新项目
ipcMain.handle('update-project', async (event, { originalName, originalPath, originalType, name, path, type }) => {
  try {
    // 如果类型改变，需要从原类型中删除，在新类型中添加
    if (originalType !== type) {
      // 从原类型中删除
      const originalConfig = await readProjectsFromFile(originalType);
      originalConfig.projects = originalConfig.projects.filter(p =>
        !(p.name === originalName && p.path === originalPath)
      );
      await saveProjectsToFile(originalType, originalConfig.projects);

      // 在新类型中添加
      const newConfig = await readProjectsFromFile(type);

      // 检查新类型中是否已存在相同名称或路径的项目
      const existingProject = newConfig.projects.find(p => p.name === name || p.path === path);
      if (existingProject) {
        throw new Error('目标类型中已存在相同名称或路径的项目');
      }

      newConfig.projects.push({ name, path });
      await saveProjectsToFile(type, newConfig.projects);
    } else {
      // 同类型更新
      const config = await readProjectsFromFile(type);
      const projectIndex = config.projects.findIndex(p =>
        p.name === originalName && p.path === originalPath
      );

      if (projectIndex === -1) {
        throw new Error('未找到要更新的项目');
      }

      // 检查是否与其他项目冲突
      const conflictProject = config.projects.find((p, index) =>
        index !== projectIndex && (p.name === name || p.path === path)
      );
      if (conflictProject) {
        throw new Error('项目名称或路径与其他项目冲突');
      }

      // 更新项目
      config.projects[projectIndex] = { name, path };
      await saveProjectsToFile(type, config.projects);
    }

    return { success: true, message: '项目更新成功' };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// IPC 通信：删除项目
ipcMain.handle('delete-project', async (event, { name, path, type }) => {
  try {
    const config = await readProjectsFromFile(type);
    const originalLength = config.projects.length;

    config.projects = config.projects.filter(p =>
      !(p.name === name && p.path === path)
    );

    if (config.projects.length === originalLength) {
      throw new Error('未找到要删除的项目');
    }

    await saveProjectsToFile(type, config.projects);

    return { success: true, message: '项目删除成功' };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// IPC 通信：执行更新命令
ipcMain.handle('execute-update', async (event, { projects, version, commands }) => {
  const mainWindow = BrowserWindow.fromWebContents(event.sender);
  
  // 发送开始执行的消息
  mainWindow.webContents.send('command-log', {
    type: 'info',
    message: `开始执行更新，版本: ${version}`,
    time: new Date().toLocaleTimeString()
  });
  
  const allProjects = [];
  let pcProjects = [];
  let mobileProjects = [];
  try {
    const pcConfig = JSON.parse(await fs.readFile(PC_CONFIG_PATH, 'utf-8'));
    pcProjects = pcConfig.projects.map((proj) => ({ ...proj, type: 'pc', dependency: '@xypaas/appEngine_pc' }));
    allProjects.push(...pcProjects);
  } catch (error) {
    throw new Error(`读取 ${PC_CONFIG_PATH} 失败：${error.message}`);
  }
  try {
    const mobileConfig = JSON.parse(await fs.readFile(MOBILE_CONFIG_PATH, 'utf-8'));
    mobileProjects = mobileConfig.projects.map((proj) => ({ ...proj, type: 'mobile', dependency: '@xypaas/appEngine_mobile' }));
    allProjects.push(...mobileProjects);
  } catch (error) {
    throw new Error(`读取 ${MOBILE_CONFIG_PATH} 失败：${error.message}`);
  }

  let selectedProjects = [];
  if (projects) {
    const projectIndexes = Array.isArray(projects) ? projects : [projects];
    for (const index of projectIndexes) {
      if (index === 'all') {
        selectedProjects = allProjects;
        break;
      } else if (index === 'all_pc') {
        selectedProjects.push(...pcProjects);
      } else if (index === 'all_mobile') {
        selectedProjects.push(...mobileProjects);
      } else {
        const idx = parseInt(index, 10);
        if (!isNaN(idx) && idx >= 0 && idx < allProjects.length) {
          selectedProjects.push(allProjects[idx]);
        }
      }
    }
  }

  selectedProjects = [...new Map(selectedProjects.map((proj) => [proj.path, proj])).values()];

  if (selectedProjects.length === 0) {
    throw new Error('未选择有效项目');
  }

  if (!version) {
    throw new Error('未输入版本号');
  }

  const selectedTypes = [...new Set(selectedProjects.map((proj) => proj.type))];
  for (const type of selectedTypes) {
    await saveVersionFile(type, version);
  }

  const skippedProjects = [];
  const executedProjects = [];

  // 设置默认命令选项（如果没有提供）
  const defaultCommands = {
    gitPull: true,
    pnpmInstall: true,
    pnpmUpdate: true,
    gitAdd: true,
    gitCommit: true,
    gitPush: true
  };
  const selectedCommands = commands || defaultCommands;

  for (const project of selectedProjects) {
    mainWindow.webContents.send('command-log', {
      type: 'info',
      message: `开始处理项目: ${project.name}`,
      time: new Date().toLocaleTimeString()
    });

    if (hasUncommittedChanges(project.path, mainWindow)) {
      const skipMessage = `${project.name} (${project.path}) - 原因：存在未提交更改`;
      skippedProjects.push(skipMessage);
      mainWindow.webContents.send('command-log', {
        type: 'warning',
        message: skipMessage,
        time: new Date().toLocaleTimeString()
      });
      continue;
    }

    let projectFailed = false;

    // Git Pull
    if (selectedCommands.gitPull) {
      const result = await executeCommand('git pull', project.path, mainWindow);
      if (!result.success) {
        skippedProjects.push(`${project.name} (${project.path}) - 原因：git pull 失败`);
        projectFailed = true;
      }
    } else {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `跳过 git pull: ${project.name}`,
        time: new Date().toLocaleTimeString()
      });
    }

    if (projectFailed) continue;

    // PNPM Install
    if (selectedCommands.pnpmInstall) {
      const result = await executeCommand('pnpm install --frozen-lockfile', project.path, mainWindow);
      if (!result.success) {
        skippedProjects.push(`${project.name} (${project.path}) - 原因：pnpm install 失败`);
        projectFailed = true;
      }
    } else {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `跳过 pnpm install: ${project.name}`,
        time: new Date().toLocaleTimeString()
      });
    }

    if (projectFailed) continue;

    // PNPM Update
    if (selectedCommands.pnpmUpdate) {
      const result = await executeCommand(`pnpm update ${project.dependency}@${version}`, project.path, mainWindow);
      if (!result.success) {
        skippedProjects.push(`${project.name} (${project.path}) - 原因：pnpm update 失败`);
        projectFailed = true;
      }
    } else {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `跳过 pnpm update: ${project.name}`,
        time: new Date().toLocaleTimeString()
      });
    }

    if (projectFailed) continue;

    // Git Add
    if (selectedCommands.gitAdd) {
      const result = await executeCommand('git add .', project.path, mainWindow);
      if (!result.success) {
        skippedProjects.push(`${project.name} (${project.path}) - 原因：git add 失败`);
        projectFailed = true;
      }
    } else {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `跳过 git add: ${project.name}`,
        time: new Date().toLocaleTimeString()
      });
    }

    if (projectFailed) continue;

    // Git Commit
    if (selectedCommands.gitCommit) {
      const commitMessage = `update: 依赖升级 ${project.dependency}@${version}`;
      const result = await executeCommand(`git commit -m "${commitMessage}"`, project.path, mainWindow);
      if (!result.success) {
        skippedProjects.push(`${project.name} (${project.path}) - 原因：git commit 失败`);
        projectFailed = true;
      }
    } else {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `跳过 git commit: ${project.name}`,
        time: new Date().toLocaleTimeString()
      });
    }

    if (projectFailed) continue;

    // Git Push
    if (selectedCommands.gitPush) {
      const result = await executeCommand('git push', project.path, mainWindow);
      if (!result.success) {
        skippedProjects.push(`${project.name} (${project.path}) - 原因：git push 失败`);
        projectFailed = true;
      }
    } else {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `跳过 git push: ${project.name}`,
        time: new Date().toLocaleTimeString()
      });
    }

    if (projectFailed) continue;

    executedProjects.push(`${project.name} (${project.path})`);
    const successMessage = `项目 ${project.name} 处理完成`;
    mainWindow.webContents.send('command-log', {
      type: 'success',
      message: successMessage,
      time: new Date().toLocaleTimeString()
    });
  }

  mainWindow.webContents.send('command-log', {
    type: 'info',
    message: `更新完成: ${executedProjects.length} 个成功, ${skippedProjects.length} 个跳过`,
    time: new Date().toLocaleTimeString()
  });

  return { executedProjects, skippedProjects };
});

// 添加错误处理
app.on('render-process-gone', (event, webContents, details) => {
  console.error('渲染进程崩溃:', details);
});

app.on('child-process-gone', (event, details) => {
  console.error('子进程崩溃:', details);
});
