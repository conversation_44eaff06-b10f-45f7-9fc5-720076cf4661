// main.js
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const { execSync } = require('child_process');

// 配置文件路径
const PC_CONFIG_PATH = path.join(__dirname, 'pcProject.json');
const MOBILE_CONFIG_PATH = path.join(__dirname, 'mobileProject.json');
const VERSION_FILE_PATH = path.join(__dirname, 'version.json');

// 检查 Git 是否有未提交更改
const hasUncommittedChanges = (projectPath, mainWindow) => {
  try {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: `检查 Git 状态: ${projectPath}`,
        time: new Date().toLocaleTimeString()
      });
    }
    
    const status = execSync('git status --porcelain', { cwd: projectPath, stdio: 'pipe' }).toString();
    const hasChanges = status.trim().length > 0;
    
    if (hasChanges && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'warning',
        message: `检测到未提交更改: ${projectPath}`,
        time: new Date().toLocaleTimeString()
      });
    }
    
    return hasChanges;
  } catch (error) {
    const errorMessage = `在 ${projectPath} 检查 Git 状态时出错：${error.message}`;
    console.error(errorMessage);
    
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'error',
        message: errorMessage,
        time: new Date().toLocaleTimeString()
      });
    }
    
    return true; // 出错时视为有未提交更改，跳过
  }
};

// 执行 shell 命令
const executeCommand = (command, projectPath, mainWindow) => {
  try {
    const logMessage = `执行命令：${command} 在 ${projectPath}`;
    console.log(logMessage);
    
    // 发送日志到渲染进程
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'info',
        message: logMessage,
        time: new Date().toLocaleTimeString()
      });
    }
    
    execSync(command, { cwd: projectPath, stdio: 'pipe' });
    
    const successMessage = `命令执行成功：${command}`;
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'success',
        message: successMessage,
        time: new Date().toLocaleTimeString()
      });
    }
    
    return { success: true };
  } catch (error) {
    const errorMessage = `在 ${projectPath} 执行 ${command} 时出错：${error.message}`;
    console.error(errorMessage);
    
    // 发送错误日志到渲染进程
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('command-log', {
        type: 'error',
        message: errorMessage,
        time: new Date().toLocaleTimeString()
      });
    }
    
    return { success: false, error: error.message };
  }
};

// 读取 version.json 文件
async function readVersionFile() {
  try {
    const content = await fs.readFile(VERSION_FILE_PATH, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    return { pc: null, mobile: null }; // 文件不存在或无效时返回默认值
  }
}

// 保存版本到 version.json
async function saveVersionFile(type, version) {
  try {
    const versionData = await readVersionFile();
    versionData[type] = version;
    await fs.writeFile(VERSION_FILE_PATH, JSON.stringify(versionData, null, 2));
  } catch (error) {
    console.error(`保存版本到 ${VERSION_FILE_PATH} 失败：${error.message}`);
  }
}

// 创建主窗口
function createWindow() {
  console.log('应用路径:', app.getAppPath());
  console.log('预加载脚本路径:', path.join(__dirname, 'preload.js'));

  const mainWindow = new BrowserWindow({
    width: 1366,
    height: 1000,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 添加错误处理
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('页面加载失败:', errorCode, errorDescription);
  });

  // 添加控制台日志捕获
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`[Renderer Console][${level}]: ${message}`);
  });

  mainWindow.loadFile(path.join(__dirname, 'views/index.html'));
  
  // // 开发模式下打开开发者工具
  // if (process.env.NODE_ENV === 'development') {
  //   mainWindow.webContents.openDevTools();
  // } else {
  //   // 生产环境下延迟加载，给预加载脚本更多时间
  //   // setTimeout(() => {
  //   //   mainWindow.webContents.send('app-ready', true);
  //   // }, 2000);
  // }
}

// Electron 应用初始化
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC 通信：加载项目数据
ipcMain.handle('load-projects', async () => {
  const allProjects = [];
  let pcProjects = [];
  let mobileProjects = [];
  try {
    const pcConfig = JSON.parse(await fs.readFile(PC_CONFIG_PATH, 'utf-8'));
    pcProjects = pcConfig.projects.map((proj) => ({ ...proj, type: 'pc', dependency: '@xypaas/appEngine_pc' }));
    allProjects.push(...pcProjects);
  } catch (error) {
    throw new Error(`读取 ${PC_CONFIG_PATH} 失败：${error.message}`);
  }
  try {
    const mobileConfig = JSON.parse(await fs.readFile(MOBILE_CONFIG_PATH, 'utf-8'));
    mobileProjects = mobileConfig.projects.map((proj) => ({ ...proj, type: 'mobile', dependency: '@xypaas/appEngine_mobile' }));
    allProjects.push(...mobileProjects);
  } catch (error) {
    throw new Error(`读取 ${MOBILE_CONFIG_PATH} 失败：${error.message}`);
  }

  const versionData = await readVersionFile();
  return {
    projects: allProjects,
    pcProjectsCount: pcProjects.length,
    mobileProjectsCount: mobileProjects.length,
    lastPcVersion: versionData.pc || '',
    lastMobileVersion: versionData.mobile || ''
  };
});

// IPC 通信：执行更新命令
ipcMain.handle('execute-update', async (event, { projects, version }) => {
  const mainWindow = BrowserWindow.fromWebContents(event.sender);
  
  // 发送开始执行的消息
  mainWindow.webContents.send('command-log', {
    type: 'info',
    message: `开始执行更新，版本: ${version}`,
    time: new Date().toLocaleTimeString()
  });
  
  const allProjects = [];
  let pcProjects = [];
  let mobileProjects = [];
  try {
    const pcConfig = JSON.parse(await fs.readFile(PC_CONFIG_PATH, 'utf-8'));
    pcProjects = pcConfig.projects.map((proj) => ({ ...proj, type: 'pc', dependency: '@xypaas/appEngine_pc' }));
    allProjects.push(...pcProjects);
  } catch (error) {
    throw new Error(`读取 ${PC_CONFIG_PATH} 失败：${error.message}`);
  }
  try {
    const mobileConfig = JSON.parse(await fs.readFile(MOBILE_CONFIG_PATH, 'utf-8'));
    mobileProjects = mobileConfig.projects.map((proj) => ({ ...proj, type: 'mobile', dependency: '@xypaas/appEngine_mobile' }));
    allProjects.push(...mobileProjects);
  } catch (error) {
    throw new Error(`读取 ${MOBILE_CONFIG_PATH} 失败：${error.message}`);
  }

  let selectedProjects = [];
  if (projects) {
    const projectIndexes = Array.isArray(projects) ? projects : [projects];
    for (const index of projectIndexes) {
      if (index === 'all') {
        selectedProjects = allProjects;
        break;
      } else if (index === 'all_pc') {
        selectedProjects.push(...pcProjects);
      } else if (index === 'all_mobile') {
        selectedProjects.push(...mobileProjects);
      } else {
        const idx = parseInt(index, 10);
        if (!isNaN(idx) && idx >= 0 && idx < allProjects.length) {
          selectedProjects.push(allProjects[idx]);
        }
      }
    }
  }

  selectedProjects = [...new Map(selectedProjects.map((proj) => [proj.path, proj])).values()];

  if (selectedProjects.length === 0) {
    throw new Error('未选择有效项目');
  }

  if (!version) {
    throw new Error('未输入版本号');
  }

  const selectedTypes = [...new Set(selectedProjects.map((proj) => proj.type))];
  for (const type of selectedTypes) {
    await saveVersionFile(type, version);
  }

  const skippedProjects = [];
  const executedProjects = [];

  for (const project of selectedProjects) {
    mainWindow.webContents.send('command-log', {
      type: 'info',
      message: `开始处理项目: ${project.name}`,
      time: new Date().toLocaleTimeString()
    });
    
    if (hasUncommittedChanges(project.path, mainWindow)) {
      const skipMessage = `${project.name} (${project.path}) - 原因：存在未提交更改`;
      skippedProjects.push(skipMessage);
      mainWindow.webContents.send('command-log', {
        type: 'warning',
        message: skipMessage,
        time: new Date().toLocaleTimeString()
      });
      continue;
    }

    if (!executeCommand('git pull', project.path, mainWindow).success) {
      skippedProjects.push(`${project.name} (${project.path}) - 原因：git pull 失败`);
      continue;
    }

    if (!executeCommand('pnpm install --frozen-lockfile', project.path, mainWindow).success) {
      skippedProjects.push(`${project.name} (${project.path}) - 原因：pnpm install 失败`);
      continue;
    }

    if (!executeCommand(`pnpm update ${project.dependency}@${version}`, project.path, mainWindow).success) {
      skippedProjects.push(`${project.name} (${project.path}) - 原因：pnpm update 失败`);
      continue;
    }

    if (!executeCommand('git add .', project.path, mainWindow).success) {
      skippedProjects.push(`${project.name} (${project.path}) - 原因：git add 失败`);
      continue;
    }

    const commitMessage = `update: 依赖升级 ${project.dependency}@${version}`;
    if (!executeCommand(`git commit -m "${commitMessage}"`, project.path, mainWindow).success) {
      skippedProjects.push(`${project.name} (${project.path}) - 原因：git commit 失败`);
      continue;
    }

    if (!executeCommand('git push', project.path, mainWindow).success) {
      skippedProjects.push(`${project.name} (${project.path}) - 原因：git push 失败`);
      continue;
    }

    executedProjects.push(`${project.name} (${project.path})`);
    mainWindow.webContents.send('command-log', {
      type: 'success',
      message: successMessage,
      time: new Date().toLocaleTimeString()
    });
  }

  mainWindow.webContents.send('command-log', {
    type: 'info',
    message: `更新完成: ${executedProjects.length} 个成功, ${skippedProjects.length} 个跳过`,
    time: new Date().toLocaleTimeString()
  });

  return { executedProjects, skippedProjects };
});

// 添加错误处理
app.on('render-process-gone', (event, webContents, details) => {
  console.error('渲染进程崩溃:', details);
});

app.on('child-process-gone', (event, details) => {
  console.error('子进程崩溃:', details);
});
