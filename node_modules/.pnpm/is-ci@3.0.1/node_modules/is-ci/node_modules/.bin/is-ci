#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/is-ci@3.0.1/node_modules/is-ci/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/is-ci@3.0.1/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/is-ci@3.0.1/node_modules/is-ci/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/is-ci@3.0.1/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin.js" "$@"
else
  exec node  "$basedir/../../bin.js" "$@"
fi
