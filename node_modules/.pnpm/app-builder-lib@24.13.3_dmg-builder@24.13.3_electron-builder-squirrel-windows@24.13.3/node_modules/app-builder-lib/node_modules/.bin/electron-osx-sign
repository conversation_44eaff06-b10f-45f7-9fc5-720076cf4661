#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules/@electron/osx-sign/bin/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules/@electron/osx-sign/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules/@electron/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules/@electron/osx-sign/bin/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules/@electron/osx-sign/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules/@electron/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/@electron+osx-sign@1.0.5/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../@electron+osx-sign@1.0.5/node_modules/@electron/osx-sign/bin/electron-osx-sign.js" "$@"
else
  exec node  "$basedir/../../../../../@electron+osx-sign@1.0.5/node_modules/@electron/osx-sign/bin/electron-osx-sign.js" "$@"
fi
