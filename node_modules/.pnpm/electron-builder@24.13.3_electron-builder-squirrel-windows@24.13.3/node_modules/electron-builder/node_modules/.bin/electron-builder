#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules/electron-builder/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules/electron-builder/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
