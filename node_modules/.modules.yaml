hoistPattern:
  - '*'
hoistedDependencies:
  /7zip-bin/5.2.0:
    7zip-bin: private
  /@develar/schema-utils/2.6.5:
    '@develar/schema-utils': private
  /@electron/asar/3.4.1:
    '@electron/asar': private
  /@electron/get/2.0.3:
    '@electron/get': private
  /@electron/notarize/2.2.1:
    '@electron/notarize': private
  /@electron/osx-sign/1.0.5:
    '@electron/osx-sign': private
  /@electron/universal/1.5.1:
    '@electron/universal': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@malept/cross-spawn-promise/1.1.1:
    '@malept/cross-spawn-promise': private
  /@malept/flatpak-bundler/0.4.0:
    '@malept/flatpak-bundler': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@sindresorhus/is/4.6.0:
    '@sindresorhus/is': private
  /@szmarczak/http-timer/4.0.6:
    '@szmarczak/http-timer': private
  /@tootallnate/once/2.0.0:
    '@tootallnate/once': private
  /@types/cacheable-request/6.0.3:
    '@types/cacheable-request': private
  /@types/debug/4.1.12:
    '@types/debug': private
  /@types/fs-extra/9.0.13:
    '@types/fs-extra': private
  /@types/http-cache-semantics/4.0.4:
    '@types/http-cache-semantics': private
  /@types/keyv/3.1.4:
    '@types/keyv': private
  /@types/ms/2.1.0:
    '@types/ms': private
  /@types/node/20.19.0:
    '@types/node': private
  /@types/plist/3.0.5:
    '@types/plist': private
  /@types/responselike/1.0.3:
    '@types/responselike': private
  /@types/verror/1.10.11:
    '@types/verror': private
  /@types/yauzl/2.10.3:
    '@types/yauzl': private
  /@xmldom/xmldom/0.8.10:
    '@xmldom/xmldom': private
  /agent-base/6.0.2:
    agent-base: private
  /ajv-keywords/3.5.2(ajv@6.12.6):
    ajv-keywords: private
  /ajv/6.12.6:
    ajv: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /app-builder-bin/4.0.0:
    app-builder-bin: private
  /app-builder-lib/24.13.3(dmg-builder@24.13.3)(electron-builder-squirrel-windows@24.13.3):
    app-builder-lib: private
  /archiver-utils/2.1.0:
    archiver-utils: private
  /archiver/5.3.2:
    archiver: private
  /argparse/2.0.1:
    argparse: private
  /assert-plus/1.0.0:
    assert-plus: private
  /astral-regex/2.0.0:
    astral-regex: private
  /async-exit-hook/2.0.1:
    async-exit-hook: private
  /async/3.2.6:
    async: private
  /asynckit/0.4.0:
    asynckit: private
  /at-least-node/1.0.0:
    at-least-node: private
  /b4a/1.6.7:
    b4a: private
  /balanced-match/1.0.2:
    balanced-match: private
  /bare-events/2.5.4:
    bare-events: private
  /bare-fs/4.1.5:
    bare-fs: private
  /bare-os/3.6.1:
    bare-os: private
  /bare-path/3.0.0:
    bare-path: private
  /bare-stream/2.6.5(bare-events@2.5.4):
    bare-stream: private
  /base64-js/1.5.1:
    base64-js: private
  /bl/4.1.0:
    bl: private
  /bluebird-lst/1.0.9:
    bluebird-lst: private
  /bluebird/3.7.2:
    bluebird: private
  /boolean/3.2.0:
    boolean: private
  /brace-expansion/2.0.1:
    brace-expansion: private
  /buffer-crc32/0.2.13:
    buffer-crc32: private
  /buffer-equal/1.0.1:
    buffer-equal: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer/5.7.1:
    buffer: private
  /builder-util-runtime/9.2.4:
    builder-util-runtime: private
  /builder-util/24.13.1:
    builder-util: private
  /bytes/3.1.2:
    bytes: private
  /cacheable-lookup/5.0.4:
    cacheable-lookup: private
  /cacheable-request/7.0.4:
    cacheable-request: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bound/1.0.4:
    call-bound: private
  /chalk/4.1.2:
    chalk: private
  /chownr/1.1.4:
    chownr: private
  /chromium-pickle-js/0.2.0:
    chromium-pickle-js: private
  /ci-info/3.9.0:
    ci-info: private
  /cli-truncate/2.1.0:
    cli-truncate: private
  /cliui/8.0.1:
    cliui: private
  /clone-response/1.0.3:
    clone-response: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /color-string/1.9.1:
    color-string: private
  /color/4.2.3:
    color: private
  /combined-stream/1.0.8:
    combined-stream: private
  /commander/5.1.0:
    commander: private
  /compare-version/0.1.2:
    compare-version: private
  /compress-commons/4.1.2:
    compress-commons: private
  /concat-map/0.0.1:
    concat-map: private
  /config-file-ts/0.2.6:
    config-file-ts: private
  /content-type/1.0.5:
    content-type: private
  /core-util-is/1.0.2:
    core-util-is: private
  /crc-32/1.2.2:
    crc-32: private
  /crc/3.8.0:
    crc: private
  /crc32-stream/4.0.3:
    crc32-stream: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /debug/2.6.9:
    debug: private
  /decompress-response/6.0.0:
    decompress-response: private
  /deep-extend/0.6.0:
    deep-extend: private
  /defer-to-connect/2.0.1:
    defer-to-connect: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /detect-libc/2.0.4:
    detect-libc: private
  /detect-node/2.1.0:
    detect-node: private
  /dir-compare/3.3.0:
    dir-compare: private
  /dmg-builder/24.13.3(electron-builder-squirrel-windows@24.13.3):
    dmg-builder: private
  /dmg-license/1.0.11:
    dmg-license: private
  /dotenv-expand/5.1.0:
    dotenv-expand: private
  /dotenv/9.0.2:
    dotenv: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ee-first/1.1.1:
    ee-first: private
  /ejs/3.1.10:
    ejs: private
  /electron-builder-squirrel-windows/24.13.3(dmg-builder@24.13.3):
    electron-builder-squirrel-windows: private
  /electron-publish/24.13.1:
    electron-publish: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /end-of-stream/1.4.4:
    end-of-stream: private
  /env-paths/2.2.1:
    env-paths: private
  /err-code/2.0.3:
    err-code: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es6-error/4.1.1:
    es6-error: private
  /escalade/3.2.0:
    escalade: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /expand-template/2.0.3:
    expand-template: private
  /extract-zip/2.0.1:
    extract-zip: private
  /extsprintf/1.4.1:
    extsprintf: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-fifo/1.3.2:
    fast-fifo: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fd-slicer/1.1.0:
    fd-slicer: private
  /filelist/1.0.4:
    filelist: private
  /foreground-child/3.3.1:
    foreground-child: private
  /form-data/4.0.3:
    form-data: private
  /fs-constants/1.0.0:
    fs-constants: private
  /fs-extra/10.1.0:
    fs-extra: private
  /fs-minipass/2.1.0:
    fs-minipass: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /function-bind/1.1.2:
    function-bind: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/5.2.0:
    get-stream: private
  /github-from-package/0.0.0:
    github-from-package: private
  /glob/10.4.5:
    glob: private
  /global-agent/3.0.0:
    global-agent: private
  /globalthis/1.0.4:
    globalthis: private
  /gopd/1.2.0:
    gopd: private
  /got/11.8.6:
    got: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /has-flag/4.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /hosted-git-info/4.1.0:
    hosted-git-info: private
  /http-cache-semantics/4.2.0:
    http-cache-semantics: private
  /http-errors/2.0.0:
    http-errors: private
  /http-proxy-agent/5.0.0:
    http-proxy-agent: private
  /http2-wrapper/1.0.3:
    http2-wrapper: private
  /https-proxy-agent/5.0.1:
    https-proxy-agent: private
  /iconv-corefoundation/1.1.7:
    iconv-corefoundation: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /ieee754/1.2.1:
    ieee754: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/1.3.8:
    ini: private
  /is-arrayish/0.3.2:
    is-arrayish: private
  /is-ci/3.0.1:
    is-ci: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /isarray/1.0.0:
    isarray: private
  /isbinaryfile/5.0.4:
    isbinaryfile: private
  /isexe/3.1.1:
    isexe: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jake/10.9.2:
    jake: private
  /js-yaml/4.1.0:
    js-yaml: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stringify-safe/5.0.1:
    json-stringify-safe: private
  /json5/2.2.3:
    json5: private
  /jsonfile/6.1.0:
    jsonfile: private
  /keyv/4.5.4:
    keyv: private
  /lazy-val/1.0.5:
    lazy-val: private
  /lazystream/1.0.1:
    lazystream: private
  /lodash.defaults/4.2.0:
    lodash.defaults: private
  /lodash.difference/4.5.0:
    lodash.difference: private
  /lodash.flatten/4.4.0:
    lodash.flatten: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.union/4.6.0:
    lodash.union: private
  /lodash/4.17.21:
    lodash: private
  /lowercase-keys/2.0.0:
    lowercase-keys: private
  /lru-cache/6.0.0:
    lru-cache: private
  /matcher/3.0.0:
    matcher: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /media-typer/0.3.0:
    media-typer: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/2.6.0:
    mime: private
  /mimic-response/3.1.0:
    mimic-response: private
  /minimatch/5.1.6:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/5.0.0:
    minipass: private
  /minizlib/2.1.2:
    minizlib: private
  /mkdirp-classic/0.5.3:
    mkdirp-classic: private
  /mkdirp/1.0.4:
    mkdirp: private
  /ms/2.0.0:
    ms: private
  /napi-build-utils/2.0.0:
    napi-build-utils: private
  /node-abi/3.75.0:
    node-abi: private
  /node-addon-api/6.1.0:
    node-addon-api: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-url/6.1.0:
    normalize-url: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /on-finished/2.4.1:
    on-finished: private
  /once/1.4.0:
    once: private
  /p-cancelable/2.1.1:
    p-cancelable: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-scurry/1.11.1:
    path-scurry: private
  /pend/1.2.0:
    pend: private
  /plist/3.1.0:
    plist: private
  /prebuild-install/7.1.3:
    prebuild-install: private
  /process-nextick-args/2.0.1:
    process-nextick-args: private
  /progress/2.0.3:
    progress: private
  /promise-retry/2.0.1:
    promise-retry: private
  /pump/3.0.2:
    pump: private
  /punycode/2.3.1:
    punycode: private
  /qs/6.13.0:
    qs: private
  /quick-lru/5.1.1:
    quick-lru: private
  /raw-body/2.5.2:
    raw-body: private
  /rc/1.2.8:
    rc: private
  /read-config-file/6.3.2:
    read-config-file: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdir-glob/1.1.3:
    readdir-glob: private
  /require-directory/2.1.1:
    require-directory: private
  /resolve-alpn/1.2.1:
    resolve-alpn: private
  /responselike/2.0.1:
    responselike: private
  /retry/0.12.0:
    retry: private
  /roarr/2.15.4:
    roarr: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /sanitize-filename/1.6.3:
    sanitize-filename: private
  /sax/1.4.1:
    sax: private
  /semver-compare/1.0.0:
    semver-compare: private
  /semver/7.7.2:
    semver: private
  /serialize-error/7.0.1:
    serialize-error: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /signal-exit/4.1.0:
    signal-exit: private
  /simple-concat/1.0.1:
    simple-concat: private
  /simple-get/4.0.1:
    simple-get: private
  /simple-swizzle/0.2.2:
    simple-swizzle: private
  /simple-update-notifier/2.0.0:
    simple-update-notifier: private
  /slice-ansi/3.0.0:
    slice-ansi: private
  /smart-buffer/4.2.0:
    smart-buffer: private
  /source-map-support/0.5.21:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /sprintf-js/1.1.3:
    sprintf-js: private
  /stat-mode/1.0.0:
    stat-mode: private
  /statuses/2.0.1:
    statuses: private
  /streamx/2.22.1:
    streamx: private
  /string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  /string_decoder/1.3.0:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-json-comments/2.0.1:
    strip-json-comments: private
  /sumchecker/3.0.1:
    sumchecker: private
  /supports-color/7.2.0:
    supports-color: private
  /tar-fs/3.0.9:
    tar-fs: private
  /tar-stream/3.1.7:
    tar-stream: private
  /tar/6.2.1:
    tar: private
  /temp-file/3.4.0:
    temp-file: private
  /text-decoder/1.2.3:
    text-decoder: private
  /tmp-promise/3.0.3:
    tmp-promise: private
  /tmp/0.2.3:
    tmp: private
  /toidentifier/1.0.1:
    toidentifier: private
  /truncate-utf8-bytes/1.0.2:
    truncate-utf8-bytes: private
  /tunnel-agent/0.6.0:
    tunnel-agent: private
  /type-fest/0.13.1:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /typescript/5.8.3:
    typescript: private
  /undici-types/6.21.0:
    undici-types: private
  /universalify/2.0.1:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /uri-js/4.4.1:
    uri-js: private
  /utf8-byte-length/1.0.5:
    utf8-byte-length: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /verror/1.10.1:
    verror: private
  /wrap-ansi/7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  /wrappy/1.0.2:
    wrappy: private
  /xmlbuilder/15.1.1:
    xmlbuilder: private
  /y18n/5.0.8:
    y18n: private
  /yallist/4.0.0:
    yallist: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yauzl/2.10.0:
    yauzl: private
  /zip-stream/4.1.1:
    zip-stream: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.9
pendingBuilds: []
prunedAt: Sat, 07 Jun 2025 02:23:35 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npm.taobao.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
