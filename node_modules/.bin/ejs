#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/ejs/bin/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/ejs/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/node_modules:/Users/<USER>/Desktop/xiangyuan/node_modules:/Users/<USER>/Desktop/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/ejs/bin/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/ejs/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/node_modules:/Users/<USER>/Desktop/xiangyuan/node_modules:/Users/<USER>/Desktop/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/Desktop/xiangyuan/xyzl/test/projects/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ejs/bin/cli.js" "$@"
else
  exec node  "$basedir/../ejs/bin/cli.js" "$@"
fi
