const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 暴露安全的API到渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  loadProjects: () => ipcRenderer.invoke('load-projects'),
  executeUpdate: (data) => ipcRenderer.invoke('execute-update', data),
  onCommandLog: (callback) => ipcRenderer.on('command-log', (_, data) => callback(data)),
  saveProject: (projectData) => ipcRenderer.invoke('save-project', projectData),
  updateProject: (projectData) => ipcRenderer.invoke('update-project', projectData),
  deleteProject: (projectData) => ipcRenderer.invoke('delete-project', projectData)
});
